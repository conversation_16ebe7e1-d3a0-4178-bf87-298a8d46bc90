import React from "react";
import {
    alpha,
    CircularProgress,
    Grid,
    IconButton,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Collapse,
    Divider,
} from "@mui/material";
import { ExpandMore, ExpandLess, SentimentVeryDissatisfied } from "@mui/icons-material";
import dayjs from "dayjs";
import theme from "../../../../theme";
import EditButton from "../../../../components/EditButton";
import CustomFooter from "../../../../components/CustomFooter";
import UnitHistoryTimeline from "./UnitHistoryTimeline";
import VesselThumbnail from "./VesselThumbnail";

const MobileVesselView = ({
    isLoading,
    filteredVessels,
    pagination,
    handlePageChange,
    handlePageSizeChange,
    handleExpandClick,
    handleEditClick,
    expandedRow,
    timezone,
}) => {
    const noRowsOverlay = () => (
        <Grid sx={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", height: "100%" }}>
            <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
            <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                No vessels available
            </Typography>
        </Grid>
    );

    return (
        <Grid
            container
            size="grow"
            sx={{
                overflow: "auto",
                display: "block",
                border: `1px solid ${theme.palette.custom.borderColor}`,
                borderRadius: "10px",
                p: "10px 24px",
            }}
        >
            <Grid container size="grow" sx={{ py: 1 }}>
                {["#", "Vessel Name", "Details"].map((col, i) => (
                    <Grid
                        key={i}
                        sx={{
                            color: col == "#" ? theme.palette.custom.darkBlue : theme.palette.custom.mainBlue,
                            minWidth: col == "#" ? "30px" : "auto",
                            flex: col == "Vessel Name" ? 1 : 0,
                            padding: 0,
                            border: "none",
                        }}
                    >
                        {col}
                    </Grid>
                ))}
            </Grid>

            {isLoading && (
                <Grid
                    container
                    size="grow"
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: { xs: "80%", sm: "80%" },
                        overflow: "auto",
                        mb: 2,
                    }}
                >
                    <CircularProgress size={40} />
                </Grid>
            )}

            {!isLoading && filteredVessels.length === 0 && (
                <Grid
                    container
                    size="grow"
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: { xs: "80%", sm: "80%" },
                        overflow: "auto",
                        mb: 2,
                    }}
                >
                    {noRowsOverlay()}
                </Grid>
            )}

            {!isLoading && filteredVessels.length !== 0 && (
                <Grid container height={{ xs: "80%", sm: "80%" }} overflow={"auto"} marginBottom={2} size="grow">
                    <TableContainer>
                        <Table sx={{ minWidth: 0 }} aria-labelledby="tableTitle">
                            <TableBody>
                                {filteredVessels.map((vessel) => (
                                    <React.Fragment key={vessel.serial}>
                                        <TableRow hover>
                                            <TableCell colSpan={5} sx={{ paddingX: "0 !important", borderBottom: 0 }}>
                                                <Grid container display={"flex"}>
                                                    <Grid container display={"flex"} flex={1} alignItems={"center"} justifyContent={"space-between"}>
                                                        <Grid
                                                            container
                                                            justifyContent={"space-between"}
                                                            alignItems={"center"}
                                                            gap={2}
                                                            width={"100%"}
                                                            flexWrap={"nowrap"}
                                                            overflow={"auto"}
                                                            color={"#FFFFFF"}
                                                        >
                                                            <Grid size="auto">
                                                                <Typography>{vessel.serial + "."}</Typography>
                                                            </Grid>
                                                            <Grid size="grow">
                                                                <Typography>{vessel.name}</Typography>
                                                            </Grid>
                                                            <Grid size="auto">
                                                                <IconButton onClick={() => handleExpandClick(vessel)} sx={{ padding: 0 }}>
                                                                    {expandedRow?._id == vessel._id ? (
                                                                        <ExpandLess
                                                                            sx={{ color: alpha("#FFFFFF", 0.6), padding: 0, marginRight: 2 }}
                                                                        />
                                                                    ) : (
                                                                        <ExpandMore
                                                                            sx={{ color: alpha("#FFFFFF", 0.6), padding: 0, marginRight: 2 }}
                                                                        />
                                                                    )}
                                                                </IconButton>
                                                            </Grid>
                                                        </Grid>
                                                    </Grid>
                                                </Grid>
                                            </TableCell>
                                        </TableRow>
                                        <TableRow>
                                            <TableCell colSpan={5} sx={{ padding: 0, borderBottom: 0 }}>
                                                <Collapse
                                                    in={expandedRow._id == vessel._id}
                                                    sx={{
                                                        width: "100%",
                                                        backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                                        borderRadius: "10px",
                                                        padding: "0 20px",
                                                        "& .MuiCollapse-wrapperInner": {
                                                            display: "flex",
                                                            flexDirection: "column",
                                                        },
                                                    }}
                                                >
                                                    <Grid
                                                        container
                                                        key={expandedRow._id}
                                                        sx={{
                                                            display: "flex",
                                                            width: "100%",
                                                            py: 2,
                                                            gap: "10px",
                                                            flexWrap: "wrap",
                                                        }}
                                                    >
                                                        <Grid size={{ xs: 12 }}>
                                                            <Grid container spacing={2}>
                                                                <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                                                                    <Grid container justifyContent={{ xs: "center", sm: "flex-start" }}>
                                                                        <VesselThumbnail
                                                                            thumbnailS3Key={expandedRow.thumbnail_s3_key}
                                                                            vesselName={expandedRow.name}
                                                                            size={"100%"}
                                                                            enableZoom={false}
                                                                        />
                                                                    </Grid>
                                                                </Grid>

                                                                <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                                                                    <Grid container spacing={1}>
                                                                        {[
                                                                            {
                                                                                label: "Unit ID",
                                                                                value: expandedRow.unit_id || "No Unit Assigned",
                                                                                color: expandedRow.unit_id ? "#FFFFFF" : theme.palette.custom.offline,
                                                                            },
                                                                            {
                                                                                label: "Status",
                                                                                value: expandedRow.is_active ? "Active" : "Inactive",
                                                                                color: expandedRow.is_active
                                                                                    ? theme.palette.success.main
                                                                                    : theme.palette.warning.main,
                                                                            },
                                                                            {
                                                                                label: "Created",
                                                                                value: dayjs(expandedRow.creation_timestamp)
                                                                                    .tz(timezone)
                                                                                    .format("MMM DD, YYYY"),
                                                                            },
                                                                            {
                                                                                label: "Created By",
                                                                                value: expandedRow.user?.name || "--",
                                                                            },
                                                                            {
                                                                                label: "Actions",
                                                                                renderCell: () => (
                                                                                    <Grid container gap={1}>
                                                                                        <Grid item>
                                                                                            <EditButton onClick={() => handleEditClick(vessel)} />
                                                                                        </Grid>
                                                                                    </Grid>
                                                                                ),
                                                                            },
                                                                        ].map((item, index) => (
                                                                            <Grid size={{ xs: 12 }} key={index}>
                                                                                <Grid container>
                                                                                    <Grid size={{ xs: 4, sm: 3 }}>
                                                                                        <Typography variant="body2" fontWeight={600} color="#FFFFFF">
                                                                                            {item.label}:
                                                                                        </Typography>
                                                                                    </Grid>
                                                                                    <Grid size={{ xs: 8, sm: 9 }}>
                                                                                        {item.renderCell ? (
                                                                                            item.renderCell()
                                                                                        ) : (
                                                                                            <Typography
                                                                                                variant="body2"
                                                                                                color={item.color || "#FFFFFF"}
                                                                                            >
                                                                                                {item.value}
                                                                                            </Typography>
                                                                                        )}
                                                                                    </Grid>
                                                                                </Grid>
                                                                            </Grid>
                                                                        ))}
                                                                    </Grid>
                                                                </Grid>
                                                            </Grid>
                                                        </Grid>

                                                        <Grid size={{ xs: 12 }} sx={{ mt: 2 }}>
                                                            <Divider sx={{ bgcolor: alpha(theme.palette.custom.borderColor, 0.3), mb: 2 }} />
                                                            <UnitHistoryTimeline unitsHistory={expandedRow.units_history} timezone={timezone} />
                                                        </Grid>
                                                    </Grid>
                                                </Collapse>
                                            </TableCell>
                                        </TableRow>
                                    </React.Fragment>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Grid>
            )}

            {!isLoading && filteredVessels.length !== 0 && (
                <Grid size={{ xs: 12 }} sx={{ mt: 1 }}>
                    <CustomFooter
                        page={pagination.page}
                        rowsPerPage={pagination.limit}
                        totalRows={pagination.total}
                        onPageChange={handlePageChange}
                        onRowsPerPageChange={handlePageSizeChange}
                    />
                </Grid>
            )}
        </Grid>
    );
};

export default MobileVesselView;
