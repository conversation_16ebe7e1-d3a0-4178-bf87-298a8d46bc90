import { Grid, Typography, CircularProgress, Box } from "@mui/material";
import { memo, useRef, useCallback, useState, useEffect, forwardRef } from "react";
import { VariableSizeList } from "react-window";
import { SentimentVeryDissatisfied } from "@mui/icons-material";
import Card from "./Card";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";

const VirtualizedCardList = forwardRef(
    ({ events, setShowDetailModal, setSelectedCard, favouriteArtifacts, vessels, isLoading, onLoadMore, hasMore, containerRef }, ref) => {
        const { screenSize } = useApp();
        const listRef = useRef();
        const [isScrolling, setIsScrolling] = useState(false);
        const [containerHeight, setContainerHeight] = useState(0);

        useEffect(() => {
            if (ref) {
                ref.current = {
                    scrollToTop: () => {
                        if (listRef.current) {
                            listRef.current.scrollTo(0);
                        }
                    },
                };
            }
        }, [ref]);

        useEffect(() => {
            const updateHeight = () => {
                if (containerRef && containerRef.current) {
                    const newHeight = containerRef.current.clientHeight;
                    setContainerHeight(isLoading ? newHeight - 70 : newHeight);
                }
            };

            updateHeight();
            const resizeObserver = new ResizeObserver(updateHeight);

            if (containerRef && containerRef.current) {
                resizeObserver.observe(containerRef.current);
            }

            return () => {
                if (containerRef && containerRef.current) {
                    resizeObserver.unobserve(containerRef.current);
                }
            };
        }, [containerRef, isLoading]);

        useEffect(() => {
            if (listRef.current) {
                listRef.current.resetAfterIndex(0);
            }
        }, [events, screenSize]);

        const getColumnCount = () => {
            if (screenSize.xs) return 1;
            if (screenSize.sm) return 2;
            if (screenSize.md) return 3;
            if (screenSize.lg) return 4;
            return 5;
        };

        const getItemSize = (index) => {
            const columnCount = getColumnCount();
            const rowIndex = Math.floor(index / columnCount);
            return rowIndex === 0 ? 350 : 350;
        };

        const checkScrollPosition = useCallback((scrollOffset, clientHeight, scrollHeight) => {
            const scrollPosition = scrollOffset + clientHeight;
            return scrollPosition >= scrollHeight * 0.8;
        }, []);

        const handleScroll = useCallback(
            ({ scrollOffset, scrollUpdateWasRequested }) => {
                if (!isLoading && onLoadMore && !scrollUpdateWasRequested && listRef.current) {
                    const { scrollHeight, clientHeight } = listRef.current._outerRef;

                    if (checkScrollPosition(scrollOffset, clientHeight, scrollHeight) && hasMore && !isLoading && !isScrolling) {
                        setIsScrolling(true);
                        onLoadMore();
                        setTimeout(() => setIsScrolling(false), 1000);
                    }
                }
            },
            [hasMore, isLoading, onLoadMore, isScrolling, checkScrollPosition],
        );

        const Row = ({ index, style }) => {
            const columnCount = getColumnCount();
            const startIndex = index * columnCount;

            return (
                <Grid container style={style} spacing={2}>
                    {Array.from({ length: columnCount }).map((_, i) => {
                        const eventIndex = startIndex + i;
                        if (eventIndex >= events.length) return null;

                        const artifact = events[eventIndex];
                        return (
                            <Grid
                                key={artifact._id}
                                size={{
                                    xs: 12,
                                    sm: 6,
                                    md: 4,
                                    lg: 3,
                                    xl: 2.4,
                                }}
                            >
                                <Card
                                    card={artifact}
                                    setShowDetailModal={setShowDetailModal}
                                    setSelectedCard={setSelectedCard}
                                    favouriteArtifacts={favouriteArtifacts}
                                    vesselInfo={vessels.find((v) => v.unit_id === artifact.unit_id)}
                                />
                            </Grid>
                        );
                    })}
                </Grid>
            );
        };

        if (isLoading && events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <CircularProgress />
                </Grid>
            );
        }

        if (events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"}>
                        <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                        <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                            No data available
                        </Typography>
                    </Grid>
                </Grid>
            );
        }

        const columnCount = getColumnCount();
        const rowCount = Math.ceil(events.length / columnCount);

        return (
            <>
                <VariableSizeList
                    ref={listRef}
                    height={containerHeight}
                    width="100%"
                    itemCount={rowCount}
                    itemSize={getItemSize}
                    overscanCount={3}
                    onScroll={handleScroll}
                >
                    {Row}
                </VariableSizeList>
                {isLoading && events.length > 0 && (
                    <Box display="flex" justifyContent="center" width={"100%"} padding={2}>
                        <CircularProgress />
                    </Box>
                )}
            </>
        );
    },
);

VirtualizedCardList.displayName = "VirtualizedCardList";
export default memo(VirtualizedCardList);
