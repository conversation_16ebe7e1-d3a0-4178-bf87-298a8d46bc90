const Vessel = require("../models/VesselManagement");
const User = require("../models/User");
const { default: mongoose, isValidObjectId } = require("mongoose");
const { uploadFileToS3, deleteFileFromS3, bucketConfigs } = require("../modules/awsS3");
const { escapeRegExp } = require("../utils/functions");

class VesselManagementService {
    async uploadVesselThumbnail(file) {
        try {
            if (!file || !file.buffer) {
                return null;
            }

            const vesselThumbnailsPath = "vessel-thumbnails";

            const s3Key = await uploadFileToS3(file, bucketConfigs.assets, vesselThumbnailsPath, { acl: "private" });

            return s3Key;
        } catch (error) {
            console.error("[VesselManagementService.uploadVesselThumbnail] Error:", error);
            throw new Error("Failed to upload vessel thumbnail to S3");
        }
    }

    async fetchAll({ page = 1, limit = 10, search = "" }) {
        try {
            const skip = (page - 1) * limit;
            const escapedSearch = escapeRegExp(search);
            const searchQuery = escapedSearch
                ? {
                      $or: [{ name: { $regex: escapedSearch, $options: "i" } }, { unit_id: { $regex: escapedSearch, $options: "i" } }],
                  }
                : {};

            const total = await Vessel.countDocuments(searchQuery);

            const vessels = await Vessel.aggregate([
                { $match: searchQuery },
                { $sort: { creation_timestamp: -1 } },
                { $skip: skip },
                { $limit: limit },
            ]);

            const userIds = [...new Set(vessels.map((v) => v.created_by.toString()))].map((id) => mongoose.Types.ObjectId(id));

            const users = await User.aggregate([{ $match: { _id: { $in: userIds } } }, { $project: { _id: 1, name: 1, email: 1 } }]);

            const usersById = users.reduce((acc, user) => {
                acc[user._id.toString()] = user;
                return acc;
            }, {});

            const vesselsWithUser = vessels.map((vessel) => ({
                ...vessel,
                user: usersById[vessel.created_by.toString()] || null,
            }));

            return {
                vessels: vesselsWithUser,
                pagination: {
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                },
            };
        } catch (error) {
            console.error("[VesselManagementService.fetchAll] Error:", error);
            throw new Error("Failed to fetch vessels");
        }
    }

    async findById({ id }) {
        try {
            if (!isValidObjectId(id)) {
                throw new Error("Invalid vessel ID");
            }

            const vessel = await Vessel.aggregate([{ $match: { _id: new require("mongoose").Types.ObjectId(id) } }]);

            const users = await User.aggregate([{ $match: { _id: vessel[0].created_by } }, { $project: { _id: 1, name: 1, email: 1 } }]);

            return {
                ...vessel[0],
                user: users[0] || null,
            };
        } catch (error) {
            console.error("[VesselManagementService.findById] Error:", error);
            throw new Error("Failed to fetch vessel");
        }
    }

    async create({ name, thumbnail_file, unit_id, is_active = true, created_by }) {
        try {
            if (unit_id && unit_id.trim() !== "") {
                const existingVessel = await Vessel.findOne({ unit_id });
                if (existingVessel) {
                    throw new Error("A vessel with this unit ID already exists");
                }
            }

            let thumbnailS3Key = null;
            if (thumbnail_file && thumbnail_file.buffer) {
                const s3Key = await this.uploadVesselThumbnail(thumbnail_file);
                if (s3Key) {
                    thumbnailS3Key = s3Key;
                }
            }

            const vessel = await Vessel.create({
                name,
                thumbnail_s3_key: thumbnailS3Key,
                unit_id: unit_id && unit_id.trim() !== "" ? unit_id.trim() : null,
                is_active,
                created_by,
            });

            return await this.findById({ id: vessel._id });
        } catch (error) {
            console.error("[VesselManagementService.create] Error:", error);
            if (error.message.includes("already exists")) {
                throw error;
            }
            throw new Error("Failed to create vessel");
        }
    }

    async update({ id, name, thumbnail_file, unit_id, is_active, remove_thumbnail }) {
        try {
            if (!isValidObjectId(id)) {
                throw new Error("Invalid vessel ID");
            }

            if (unit_id && unit_id.trim() !== "") {
                const existingVessel = await Vessel.findOne({
                    unit_id: unit_id.trim(),
                    _id: { $ne: id },
                });
                if (existingVessel) {
                    throw new Error("A vessel with this unit ID already exists");
                }
            }

            const vessel = await Vessel.findById(id);
            if (!vessel) {
                throw new Error("Vessel not found");
            }

            let thumbnailS3Key = undefined;

            if (thumbnail_file && thumbnail_file.buffer) {
                if (vessel.thumbnail_s3_key) {
                    await deleteFileFromS3(bucketConfigs.assets, vessel.thumbnail_s3_key);
                }

                const s3Key = await this.uploadVesselThumbnail(thumbnail_file);
                if (s3Key) {
                    thumbnailS3Key = s3Key;
                } else {
                    thumbnailS3Key = null;
                }
            } else if (remove_thumbnail === "true") {
                if (vessel.thumbnail_s3_key) {
                    await deleteFileFromS3(bucketConfigs.assets, vessel.thumbnail_s3_key);
                }
                thumbnailS3Key = null;
            }

            if (name) vessel.name = name;
            if (thumbnailS3Key !== undefined) vessel.thumbnail_s3_key = thumbnailS3Key;
            if (unit_id !== undefined) vessel.unit_id = unit_id && unit_id.trim() !== "" ? unit_id.trim() : null;
            if (is_active) vessel.is_active = is_active;

            await vessel.save();

            return await this.findById({ id: vessel._id });
        } catch (error) {
            console.error("[VesselManagementService.update] Error:", error);
            if (error.message.includes("already exists") || error.message.includes("not found")) {
                throw error;
            }
            throw new Error("Failed to update vessel");
        }
    }
}

const vesselManagementService = new VesselManagementService();

module.exports = vesselManagementService;
