import { useState } from "react";
import { Grid, IconButton, Typography, Tooltip, Modal } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { SentimentVeryDissatisfied, History } from "@mui/icons-material";
import dayjs from "dayjs";
import theme from "../../../../theme";
import EditButton from "../../../../components/EditButton";
import CustomFooter from "../../../../components/CustomFooter";
import UnitHistoryTimeline from "./UnitHistoryTimeline";
import VesselThumbnail from "./VesselThumbnail";
import ModalContainer from "../../../../components/ModalContainer";

const DesktopVesselView = ({ isLoading, filteredVessels, pagination, handlePageChange, handlePageSizeChange, handleEditClick, timezone }) => {
    const [showUnitHistoryModal, setShowUnitHistoryModal] = useState(false);
    const [selectedVesselForHistory, setSelectedVesselForHistory] = useState(null);

    const handleHistoryClick = (vessel) => {
        setSelectedVesselForHistory(vessel);
        setShowUnitHistoryModal(true);
    };

    const handleCloseHistoryModal = () => {
        setShowUnitHistoryModal(false);
        setSelectedVesselForHistory(null);
    };

    const columns = [
        { field: "serial", headerName: "", maxWidth: 50, renderCell: ({ row }) => row.serial + "." },
        {
            field: "name",
            headerName: "Vessel Name",
            flex: 1,
            minWidth: 250,
            renderCell: ({ row }) => (
                <Grid container alignItems="center" sx={{ height: "100%", gap: 1 }}>
                    <Typography fontSize={"14px"} fontWeight={500}>
                        {row.name}
                    </Typography>
                </Grid>
            ),
        },
        {
            field: "unit_id",
            headerName: "Unit ID",
            flex: 1,
            renderCell: ({ row }) => (
                <Grid container alignItems="center" sx={{ height: "100%", gap: 1 }}>
                    <Typography fontSize={"14px"} fontWeight={400} color={row.unit_id ? "inherit" : theme.palette.custom.offline}>
                        {row.unit_id || "No Unit Assigned"}
                    </Typography>
                </Grid>
            ),
        },
        {
            field: "is_active",
            headerName: "Status",
            flex: 1,
            renderCell: ({ row }) => (
                <Grid container alignItems="center" sx={{ height: "100%", gap: 1 }}>
                    <Typography fontSize={"14px"} fontWeight={400} color={row.is_active ? theme.palette.success.main : theme.palette.warning.main}>
                        {row.is_active ? "Active" : "Inactive"}
                    </Typography>
                </Grid>
            ),
        },
        {
            field: "creation_timestamp",
            headerName: "Created",
            flex: 1,
            valueGetter: (v) => dayjs(v).tz(timezone).format("MMM DD, YYYY"),
        },
        {
            field: "user",
            headerName: "Created By",
            flex: 1,
            valueGetter: (v) => v?.name || "--",
        },
        {
            field: "actions",
            headerName: "Actions",
            flex: 1,
            headerAlign: "center",
            renderCell: (params) => (
                <Grid container justifyContent="center" sx={{ gap: 1 }}>
                    <Grid>
                        <Tooltip title="View Unit History">
                            <IconButton
                                onClick={() => handleHistoryClick(params.row)}
                                sx={{
                                    color: theme.palette.custom.offline,
                                    "&:hover": { color: theme.palette.custom.mainBlue },
                                }}
                            >
                                <History />
                            </IconButton>
                        </Tooltip>
                    </Grid>
                    <Grid>
                        <EditButton onClick={() => handleEditClick(params.row)} />
                    </Grid>
                </Grid>
            ),
            sortable: false,
        },
    ];

    const columnsWithoutFilters = [
        ...columns.map((col) => ({
            ...col,
            filterable: false,
            resizable: false,
            disableColumnMenu: true,
            disableReorder: true,
            disableExport: true,
            flex: 1,
        })),
    ];

    const noRowsOverlay = () => (
        <Grid sx={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", height: "100%" }}>
            <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
            <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                No vessels available
            </Typography>
        </Grid>
    );

    return (
        <>
            <Grid size="grow" sx={{ overflow: "auto" }}>
                <DataGrid
                    loading={isLoading}
                    disableRowSelectionOnClick
                    rows={filteredVessels}
                    columns={columnsWithoutFilters}
                    getRowId={(row) => row._id}
                    slots={{
                        footer: () => (
                            <CustomFooter
                                page={pagination.page}
                                rowsPerPage={pagination.limit}
                                totalRows={pagination.total}
                                onPageChange={handlePageChange}
                                onRowsPerPageChange={handlePageSizeChange}
                            />
                        ),
                        noRowsOverlay,
                    }}
                />
            </Grid>

            <Modal open={showUnitHistoryModal} onClose={handleCloseHistoryModal}>
                <ModalContainer
                    title={selectedVesselForHistory ? `Unit History - ${selectedVesselForHistory.name}` : "Unit History"}
                    onClose={handleCloseHistoryModal}
                >
                    {selectedVesselForHistory && (
                        <Grid container sx={{ gap: 3, minWidth: { xs: 300, sm: 600, md: 800 } }}>
                            <Grid container alignItems="center" sx={{ gap: 2 }}>
                                <VesselThumbnail
                                    thumbnailS3Key={selectedVesselForHistory.thumbnail_s3_key}
                                    vesselName={selectedVesselForHistory.name}
                                    size={200}
                                    enableZoom={true}
                                />
                                <Grid>
                                    <Typography variant="h6" fontWeight={600} color="#FFFFFF">
                                        {selectedVesselForHistory.name}
                                    </Typography>
                                    <Typography variant="body2" color={theme.palette.custom.offline}>
                                        Unit ID: {selectedVesselForHistory.unit_id || "No Unit Assigned"}
                                    </Typography>
                                    <Typography variant="body2" color={theme.palette.custom.offline}>
                                        Status: {selectedVesselForHistory.is_active ? "Active" : "Inactive"}
                                    </Typography>
                                </Grid>
                            </Grid>

                            <Grid size={{ xs: 12 }}>
                                <UnitHistoryTimeline unitsHistory={selectedVesselForHistory.units_history} timezone={timezone} />
                            </Grid>
                        </Grid>
                    )}
                </ModalContainer>
            </Modal>
        </>
    );
};

export default DesktopVesselView;
