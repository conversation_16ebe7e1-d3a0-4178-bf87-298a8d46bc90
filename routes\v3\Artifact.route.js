const express = require("express");
const router = express.Router();
const { body } = require("express-validator");
const mongoose = require("mongoose");
const assignEndpointId = require("../../middlewares/assignEndpointId");
const isAuthenticated = require("../../middlewares/auth");
const { validateData } = require("../../middlewares/validator");
const { validateError } = require("../../utils/functions");
const { endpointIds } = require("../../utils/endpointIds");
const limitPromise = require("../../modules/pLimit");
const ArtifactFavourites = require("../../models/ArtifactFavourites");
const db = require("../../modules/db");
const Vessel = require("../../models/VesselManagement");
const { default: rateLimit } = require("express-rate-limit");
const compression = require("compression");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselId",
    assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACTS_V3),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v) => v.map((id) => mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("favourites")
                    .isInt({ min: 0, max: 1 })
                    .optional()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselId } = req.params;
            const { startTimestamp, endTimestamp, excludeIds, favourites } = req.body;
            console.log(`/v3/artifacts ${vesselId}`, startTimestamp, endTimestamp);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await Vessel.findById(vesselId);
            if (!vessel) return res.status(400).json({ message: "Vessel does not exist" });

            const unitId = vessel.unit_id;
            if (!unitId) return res.status(400).json({ message: "Vessel has no associated unit_id" });

            const query = {};
            query.unit_id = unitId;

            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };
            query.location = { $ne: null };
            query.vessel_presence = true;
            query.super_category = { $ne: null };

            const artifacts = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/v3/artifacts ${vesselId} querying DB`);

                const cursor = db.qmai.collection("analysis_results").find(query, {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        aws_region: 1,
                        image_path: 1,
                        video_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        others: 1,
                        timestamp: 1,
                        text_extraction: 1,
                        weapons: 1,
                        imo_number: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                return await cursor.toArray();
            });
            console.log(`/v3/artifacts ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/v3/artifacts ${vesselId} received ${artifacts?.length} artifacts`);

            let favouritesArtifacts = [];
            // this condition will not work with API Key
            if (favourites && req.user) {
                const rawFavourites = await ArtifactFavourites.find({
                    user_id: req.user._id,
                }).select("_id artifact_id");
                const artifactIds = rawFavourites.map((fav) => fav.artifact_id);

                const allowedArtifactIds = await db.qmai
                    .collection("analysis_results")
                    .find({
                        _id: { $in: artifactIds },
                        unit_id: unitId,
                    })
                    .project({ _id: 1 })
                    .toArray();

                const allowedIdsSet = new Set(allowedArtifactIds.map((a) => a._id.toString()));

                favouritesArtifacts = rawFavourites
                    .filter((fav) => allowedIdsSet.has(fav.artifact_id.toString()))
                    .map((fav) => ({
                        _id: fav._id,
                        artifact_id: fav.artifact_id,
                    }));
            }

            if (isClosed) return res.end();
            res.json({
                artifacts,
                favouritesArtifacts,
            });
            console.log(`/v3/artifacts ${vesselId} time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * /v3/artifacts/{vesselId}:
 *   post:
 *     summary: Fetch artifacts and favourites artifacts by vessel ID
 *     description: Retrieves a list of artifacts for a specific vessel using vessel_id instead of unit_id within a given time range. Supports filtering by time range, exclusion of specific IDs, and the option to include favourite artifacts.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselId
 *         required: true
 *         schema:
 *           type: string
 *         description: The MongoDB ObjectId of the vessel to fetch artifacts for.
 *         example: "507f1f77bcf86cd799439011"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The start unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726876800000
 *               endTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The end unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726963200000
 *               excludeIds:
 *                 required: false
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *               favourites:
 *                 required: false
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: Whether to include favourite artifacts (1 for true, 0 for false).
 *                 example: 1
 *     responses:
 *       200:
 *         description: A list of artifacts and optionally favourite artifacts for the specified vessel.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 artifacts:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Artifact'
 *                 favouritesArtifacts:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       artifact_id:
 *                         type: string
 *                         description: The ID of the favourite artifact.
 *       400:
 *         description: Invalid request parameters or vessel ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this vessel
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this vessel.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
